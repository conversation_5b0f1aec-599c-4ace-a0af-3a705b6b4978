import React from 'react';
import { render, screen } from '@testing-library/react';

// Mock data for testing
const mockSapData = [
    {
        id: 508,
        title: "Hazardous Waste",
        newEfSubcategory2s: [
            {
                id: 934,
                title: "Others"
            },
            {
                id: 923,
                title: "Used Oil"
            }
        ]
    },
    {
        id: 509,
        title: "Non-Hazardous Waste",
        newEfSubcategory2s: [
            {
                id: 947,
                title: "Others"
            },
            {
                id: 936,
                title: "Paper - all types"
            }
        ]
    }
];

// Mock the extractDataFunction logic for testing
const extractDataFunction = (data, sapData) => {
    let result = [];
    
    for (const item of data) {
        // Get category and type titles for building uniqueId
        const categoryData = sapData.find(cat => cat.id === item.wasteCategory);
        const categoryTitle = categoryData ? categoryData.title : '';

        let typeTitle = '';
        if (categoryData && categoryData.newEfSubcategory2s) {
            const typeData = categoryData.newEfSubcategory2s.find(type => type.id === item.wasteType);
            typeTitle = typeData ? typeData.title : '';
            
            // Add "Others" description if applicable
            if (typeData && typeData.title === "Others" && item.wasteTypeOther) {
                typeTitle += ` (${item.wasteTypeOther})`;
            }
        }

        // 1. Generation Record (dataType: 2)
        const generationUniqueId = `${item.wasteCategory}-${item.wasteType}-1`;
        const generationTitle = `${categoryTitle} > ${typeTitle}`;
        result.push({
            title: generationTitle,
            label: generationTitle,
            value: item.quantity || 0,
            subCategory1: item.wasteCategory,
            subCategory2: item.wasteType,
            subCategory3: null,
            subCategory4: null,
            currentId: parseInt(item.wasteType),
            parentId: parseInt(item.wasteCategory),
            uniqueId: generationUniqueId,
            formType: 2,
            dataType: 2, // Generation
            uom: "MT",
            isNull: false,
            maskId: item.id
        });
    }
    
    return result;
};

describe('Waste Title Generation', () => {
    test('should generate correct title for Hazardous waste with Others type and description', () => {
        const testData = [
            {
                id: 1,
                wasteCategory: 508, // Hazardous Waste
                wasteType: 934, // Others
                wasteTypeOther: "Sludge",
                quantity: 10
            }
        ];

        const result = extractDataFunction(testData, mockSapData);
        
        expect(result).toHaveLength(1);
        expect(result[0].title).toBe("Hazardous Waste > Others (Sludge)");
        expect(result[0].label).toBe("Hazardous Waste > Others (Sludge)");
    });

    test('should generate correct title for Non-Hazardous waste with Others type and description', () => {
        const testData = [
            {
                id: 1,
                wasteCategory: 509, // Non-Hazardous Waste
                wasteType: 947, // Others
                wasteTypeOther: "Organic Waste",
                quantity: 5
            }
        ];

        const result = extractDataFunction(testData, mockSapData);
        
        expect(result).toHaveLength(1);
        expect(result[0].title).toBe("Non-Hazardous Waste > Others (Organic Waste)");
        expect(result[0].label).toBe("Non-Hazardous Waste > Others (Organic Waste)");
    });

    test('should generate correct title for waste without Others type', () => {
        const testData = [
            {
                id: 1,
                wasteCategory: 508, // Hazardous Waste
                wasteType: 923, // Used Oil
                wasteTypeOther: "",
                quantity: 15
            }
        ];

        const result = extractDataFunction(testData, mockSapData);
        
        expect(result).toHaveLength(1);
        expect(result[0].title).toBe("Hazardous Waste > Used Oil");
        expect(result[0].label).toBe("Hazardous Waste > Used Oil");
    });

    test('should generate correct title for Others type without description', () => {
        const testData = [
            {
                id: 1,
                wasteCategory: 508, // Hazardous Waste
                wasteType: 934, // Others
                wasteTypeOther: "", // No description
                quantity: 8
            }
        ];

        const result = extractDataFunction(testData, mockSapData);
        
        expect(result).toHaveLength(1);
        expect(result[0].title).toBe("Hazardous Waste > Others");
        expect(result[0].label).toBe("Hazardous Waste > Others");
    });

    test('should handle multiple waste entries correctly', () => {
        const testData = [
            {
                id: 1,
                wasteCategory: 508, // Hazardous Waste
                wasteType: 934, // Others
                wasteTypeOther: "Sludge",
                quantity: 10
            },
            {
                id: 2,
                wasteCategory: 509, // Non-Hazardous Waste
                wasteType: 936, // Paper - all types
                wasteTypeOther: "",
                quantity: 5
            }
        ];

        const result = extractDataFunction(testData, mockSapData);
        
        expect(result).toHaveLength(2);
        expect(result[0].title).toBe("Hazardous Waste > Others (Sludge)");
        expect(result[1].title).toBe("Non-Hazardous Waste > Paper - all types");
    });
});
